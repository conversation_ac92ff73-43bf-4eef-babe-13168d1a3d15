<?php declare(strict_types = 1);

// odsl-/Users/<USER>/Herd/workos-sac/app
return \PHPStan\Cache\CacheItem::__set_state(array(
   'variableKey' => 'v1',
   'data' => 
  array (
    '/Users/<USER>/Herd/workos-sac/app/Providers/AppServiceProvider.php' => 
    array (
      0 => '0db828116526d0d96c3a3fdb0450c3f19090469b',
      1 => 
      array (
        0 => 'app\\providers\\appserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
        2 => 'app\\providers\\configurecarbon',
        3 => 'app\\providers\\configurecommands',
        4 => 'app\\providers\\configuremodels',
        5 => 'app\\providers\\configurepassworddefaults',
        6 => 'app\\providers\\configuresqlitedb',
        7 => 'app\\providers\\configureurl',
        8 => 'app\\providers\\configurevite',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Providers/VoltServiceProvider.php' => 
    array (
      0 => '65a26a795005f380bc32bceec5c24b770dc58dbc',
      1 => 
      array (
        0 => 'app\\providers\\voltserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Providers/FolioServiceProvider.php' => 
    array (
      0 => '8cd87d7f787d11d68c1ff62e60629e3a9a97d365',
      1 => 
      array (
        0 => 'app\\providers\\folioserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/User.php' => 
    array (
      0 => 'f81458de60b2cf50785d44619c208484a94552a1',
      1 => 
      array (
        0 => 'app\\models\\user',
      ),
      2 => 
      array (
        0 => 'app\\models\\casts',
        1 => 'app\\models\\getsecondarykeytype',
        2 => 'app\\models\\initials',
        3 => 'app\\models\\getslugoptions',
        4 => 'app\\models\\getroutekeyname',
        5 => 'app\\models\\getactivitylogoptions',
        6 => 'app\\models\\gettotalcommentscount',
        7 => 'app\\models\\getrecentcomments',
        8 => 'app\\models\\subscribetoallcomments',
        9 => 'app\\models\\subscribetorepliesonly',
        10 => 'app\\models\\issubscribedtocomments',
        11 => 'app\\models\\getcommentsubscriptions',
        12 => 'app\\models\\gettotalreactionscount',
        13 => 'app\\models\\getfavoritereaction',
        14 => 'app\\models\\hasreactedtocomment',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Http/Controllers/Controller.php' => 
    array (
      0 => '75cadca8afa5982965d1ac316df3c693271b4902',
      1 => 
      array (
        0 => 'app\\http\\controllers\\controller',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Enums/SecondaryKeyType.php' => 
    array (
      0 => '999884fa02a91ed3b04101d5a58c4583b7de98fa',
      1 => 
      array (
        0 => 'app\\enums\\secondarykeytype',
      ),
      2 => 
      array (
        0 => 'app\\enums\\default',
        1 => 'app\\enums\\getlabel',
        2 => 'app\\enums\\getcolor',
        3 => 'app\\enums\\getdescription',
        4 => 'app\\enums\\geticon',
        5 => 'app\\enums\\usecases',
        6 => 'app\\enums\\storageinfo',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Providers/Filament/AdminPanelProvider.php' => 
    array (
      0 => 'fd1934dc4f020d0da2b1655a5428aeaaef50765a',
      1 => 
      array (
        0 => 'app\\providers\\filament\\adminpanelprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\filament\\panel',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Traits/HasSecondaryUniqueKey.php' => 
    array (
      0 => '6734eb7a6735b1d33c13404d813e6d08bfba4c60',
      1 => 
      array (
        0 => 'app\\traits\\hassecondaryuniquekey',
      ),
      2 => 
      array (
        0 => 'app\\traits\\boothassecondaryuniquekey',
        1 => 'app\\traits\\findbysecondarykey',
        2 => 'app\\traits\\findbysecondarykeyorfail',
        3 => 'app\\traits\\generatesecondarykey',
        4 => 'app\\traits\\getsecondarykeytype',
        5 => 'app\\traits\\setsecondarykeytype',
        6 => 'app\\traits\\getsecondarykeycolumn',
        7 => 'app\\traits\\scopebysecondarykey',
        8 => 'app\\traits\\getroutekeyname',
        9 => 'app\\traits\\getkeytypeinfo',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Traits/Categorizable.php' => 
    array (
      0 => '2cad2aeec34b9590bc268f7fa1e660e32ce9c7c9',
      1 => 
      array (
        0 => 'app\\traits\\categorizable',
      ),
      2 => 
      array (
        0 => 'app\\traits\\categories',
        1 => 'app\\traits\\categoriesbytype',
        2 => 'app\\traits\\attachcategory',
        3 => 'app\\traits\\attachcategories',
        4 => 'app\\traits\\detachcategory',
        5 => 'app\\traits\\synccategories',
        6 => 'app\\traits\\synccategoriesbytype',
        7 => 'app\\traits\\scopewithcategories',
        8 => 'app\\traits\\scopewithcategorytypes',
        9 => 'app\\traits\\scopewithoutcategories',
        10 => 'app\\traits\\hascategorytype',
        11 => 'app\\traits\\getcategorynames',
        12 => 'app\\traits\\getcategoriesbytypenames',
        13 => 'app\\traits\\getprimarycategory',
        14 => 'app\\traits\\setprimarycategory',
        15 => 'app\\traits\\getcategorieswithpaths',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Enums/CategoryType.php' => 
    array (
      0 => '9bd8d70a72aabd6f15ee733f08dec1cea1e5a21f',
      1 => 
      array (
        0 => 'app\\enums\\categorytype',
      ),
      2 => 
      array (
        0 => 'app\\enums\\label',
        1 => 'app\\enums\\color',
        2 => 'app\\enums\\icon',
        3 => 'app\\enums\\validationrules',
        4 => 'app\\enums\\defaultcategories',
        5 => 'app\\enums\\toarray',
        6 => 'app\\enums\\formodel',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Category.php' => 
    array (
      0 => 'c8626c874aa07ae3ae42df8b0014d1839a150e7f',
      1 => 
      array (
        0 => 'app\\models\\category',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/InvoiceLine.php' => 
    array (
      0 => '1cb74443a71101b3e62fbcf91f6e6be249245a6f',
      1 => 
      array (
        0 => 'app\\models\\invoiceline',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Playlist.php' => 
    array (
      0 => '832910d5d2b42831c9ce1d51c1f27698c3ec5927',
      1 => 
      array (
        0 => 'app\\models\\playlist',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/MediaType.php' => 
    array (
      0 => '641b26dc7df1a3bfadc07cfc1f0f0457b28494f2',
      1 => 
      array (
        0 => 'app\\models\\mediatype',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Track.php' => 
    array (
      0 => 'cd9195734a29e0be44c7226af39c3d7855db8879',
      1 => 
      array (
        0 => 'app\\models\\track',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Customer.php' => 
    array (
      0 => '2e14aed4a73b60ba4f1ca77acb0bbf6468c6a3f6',
      1 => 
      array (
        0 => 'app\\models\\customer',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Invoice.php' => 
    array (
      0 => 'e32391e6ede2fd0e92d5d8da51284721428189a3',
      1 => 
      array (
        0 => 'app\\models\\invoice',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Employee.php' => 
    array (
      0 => 'c1d1b6a4de47aeee510da9e5d85c7e3c7d250bab',
      1 => 
      array (
        0 => 'app\\models\\employee',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Album.php' => 
    array (
      0 => '0c7f9445ec3626c1da3239ddf51a7d93c48414da',
      1 => 
      array (
        0 => 'app\\models\\album',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Artist.php' => 
    array (
      0 => 'aadb14180a74a7191a72ded631a4fdda301fda66',
      1 => 
      array (
        0 => 'app\\models\\artist',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
  ),
));