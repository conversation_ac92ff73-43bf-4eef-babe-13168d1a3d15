<?php

declare(strict_types=1);

namespace App\Enums;

enum CategoryType: string
{
    case ERA = 'era';
    case GENRE = 'genre';
    case INSTRUMENT = 'instrument';
    case LANGUAGE = 'language';
    case MOOD = 'mood';
    case OCCASION = 'occasion';
    case THEME = 'theme';

    /**
     * Get the display label for the category type.
     */
    public function label(): string
    {
        return match ($this) {
            self::ERA => 'Time Period',
            self::GENRE => 'Music Genre',
            self::INSTRUMENT => 'Instrument Focus',
            self::LANGUAGE => 'Language',
            self::MOOD => 'Mood & Emotion',
            self::OCCASION => 'Occasion & Event',
            self::THEME => 'Theme & Style',
        };
    }

    /**
     * Get the color for UI representation.
     */
    public function color(): string
    {
        return match ($this) {
            self::ERA => '#96CEB4',        // Mint Green
            self::GENRE => '#FF6B6B',      // Coral Red
            self::INSTRUMENT => '#FFEAA7', // Warm Yellow
            self::LANGUAGE => '#DDA0DD',   // Plum
            self::MOOD => '#4ECDC4',       // Turquoise
            self::OCCASION => '#F8C471',   // Peach
            self::THEME => '#45B7D1',      // Sky Blue
        };
    }

    /**
     * Get the Font Awesome icon for visual identification.
     */
    public function icon(): string
    {
        return match ($this) {
            self::ERA => 'fas fa-clock',
            self::GENRE => 'fas fa-music',
            self::INSTRUMENT => 'fas fa-guitar',
            self::LANGUAGE => 'fas fa-globe',
            self::MOOD => 'fas fa-heart',
            self::OCCASION => 'fas fa-calendar-alt',
            self::THEME => 'fas fa-palette',
        };
    }

    /**
     * Get validation rules for this category type.
     */
    public function validationRules(): array
    {
        return match ($this) {
            self::ERA => [
                'max_depth' => 2,
                'allowed_parents' => [self::ERA],
                'required_fields' => ['name', 'start_year'],
            ],
            self::GENRE => [
                'max_depth' => 3,
                'allowed_parents' => [self::GENRE],
                'required_fields' => ['name', 'description'],
            ],
            self::INSTRUMENT => [
                'max_depth' => 3,
                'allowed_parents' => [self::INSTRUMENT],
                'required_fields' => ['name'],
            ],
            self::LANGUAGE => [
                'max_depth' => 1,
                'allowed_parents' => [],
                'required_fields' => ['name', 'iso_code'],
            ],
            self::MOOD => [
                'max_depth' => 2,
                'allowed_parents' => [self::MOOD],
                'required_fields' => ['name'],
            ],
            self::OCCASION => [
                'max_depth' => 2,
                'allowed_parents' => [self::OCCASION],
                'required_fields' => ['name'],
            ],
            self::THEME => [
                'max_depth' => 2,
                'allowed_parents' => [self::THEME],
                'required_fields' => ['name'],
            ],
        };
    }

    /**
     * Get default category suggestions for seeding.
     */
    public function defaultCategories(): array
    {
        return match ($this) {
            self::GENRE => [
                'Rock' => ['Hard Rock', 'Soft Rock', 'Progressive Rock'],
                'Jazz' => ['Smooth Jazz', 'Bebop', 'Fusion'],
                'Electronic' => ['House', 'Techno', 'Ambient'],
                'Classical' => ['Baroque', 'Romantic', 'Modern'],
                'Hip-Hop' => ['East Coast', 'West Coast', 'Trap'],
            ],
            self::MOOD => [
                'Energetic' => ['High Energy', 'Motivational'],
                'Relaxing' => ['Calm', 'Peaceful'],
                'Melancholic' => ['Sad', 'Nostalgic'],
                'Upbeat' => ['Happy', 'Cheerful'],
            ],
            self::THEME => [
                'Workout' => ['Cardio', 'Strength Training'],
                'Study' => ['Focus', 'Background'],
                'Party' => ['Dance', 'Celebration'],
                'Romance' => ['Love Songs', 'Intimate'],
            ],
            self::ERA => [
                '1960s' => [],
                '1970s' => [],
                '1980s' => [],
                '1990s' => [],
                '2000s' => [],
                '2010s' => [],
                '2020s' => [],
            ],
            self::INSTRUMENT => [
                'Piano' => ['Solo Piano', 'Piano Ensemble'],
                'Guitar' => ['Acoustic Guitar', 'Electric Guitar'],
                'Orchestral' => ['Symphony', 'Chamber'],
                'Electronic' => ['Synthesizer', 'Digital'],
            ],
            self::LANGUAGE => [
                'English' => [],
                'Spanish' => [],
                'French' => [],
                'German' => [],
                'Italian' => [],
                'Japanese' => [],
                'Instrumental' => [],
            ],
            self::OCCASION => [
                'Wedding' => ['Ceremony', 'Reception'],
                'Birthday' => ['Children', 'Adult'],
                'Holiday' => ['Christmas', 'Halloween'],
                'Corporate' => ['Presentation', 'Networking'],
            ],
        };
    }

    /**
     * Get all category types as array.
     */
    public static function toArray(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }

    /**
     * Get category types suitable for a specific model.
     */
    public static function forModel(string $modelClass): array
    {
        return match ($modelClass) {
            'App\Models\Album' => [self::GENRE, self::MOOD, self::THEME, self::ERA, self::LANGUAGE],
            'App\Models\Artist' => [self::GENRE, self::ERA, self::INSTRUMENT],
            'App\Models\Customer' => [self::GENRE, self::MOOD], // Preferences only
            'App\Models\Playlist' => [self::MOOD, self::THEME, self::OCCASION],
            'App\Models\Track' => [self::GENRE, self::MOOD, self::THEME, self::INSTRUMENT, self::LANGUAGE, self::OCCASION],
            default => self::cases(),
        };
    }
}
